from fastapi import APIRouter, Depends, HTTPException, Request, Form, Query
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session
from sqlalchemy import text, select
from typing import List, Optional
import pickle
from enum import Enum as PyEnum

from app.db.session import get_db
from app.admin_web.admin_panel import get_current_admin_user
from app.admin_web.admin_panel import get_base_context, templates
from app.db import models
from app import schemas
from app.logging import get_logger
from app.db.models.source import (
    HackerNewsSource, RssSource, EmailSource, 
    SourceProvider, UserSource, EmailSourceFailedToExtract
)
from app.db.models.article import CompleteArticle, SummarizedArticle, IncompleteArticle
from app.db.models import ArticleProcessStatus, ProcessStatus
from app.db.models.user import User

logger = get_logger()
router = APIRouter()

# Enum for source types to be used in API paths
class SourceTypePath(str, PyEnum): # PyEnum should now be defined
    hackernews = "hackernews"
    rss = "rss"
    email = "email"

# Schemas for task chain diagnosis
class TaskChainIssue(schemas.BaseModel): # schemas should now be defined
    step_name: str
    issue_description: str
    recommended_retry_task_name: Optional[str] = None # Optional should now be defined
    recommended_retry_task_args: Optional[list] = None
    recommended_retry_task_kwargs: Optional[dict] = None

class TaskChainDiagnosis(schemas.BaseModel): # schemas should now be defined
    original_record_id: int
    source_type: str
    status_summary: str
    issues: List[TaskChainIssue] = []

class ProblematicChainInfo(schemas.BaseModel):
    original_record_id: int
    status_summary: str
    first_issue_step: Optional[str] = None
    first_issue_description: Optional[str] = None

# Helper function to add an issue
def _add_issue(issues: List[TaskChainIssue], step: str, description: str, task_name: Optional[str] = None, task_args: Optional[list] = None, task_kwargs: Optional[dict] = None):
    issues.append(TaskChainIssue(
        step_name=step,
        issue_description=description,
        recommended_retry_task_name=task_name,
        recommended_retry_task_args=task_args,
        recommended_retry_task_kwargs=task_kwargs
    ))

async def _diagnose_hackernews_chain(original_record_id: int, db: Session) -> TaskChainDiagnosis:
    diagnosis = TaskChainDiagnosis(
        original_record_id=original_record_id,
        source_type=SourceTypePath.hackernews.value,
        status_summary="Starting diagnosis...",
        issues=[]
    )

    # Step 1: Check HackerNewsSource
    hn_source = db.get(HackerNewsSource, original_record_id)
    if not hn_source:
        _add_issue(diagnosis.issues, "HackerNewsSource", f"HackerNewsSource record with ID {original_record_id} not found.")
        diagnosis.status_summary = "Error: Original HackerNewsSource record not found."
        return diagnosis

    if not hn_source.extracted:
        _add_issue(diagnosis.issues, "HackerNewsSource Extraction",
                   f"HackerNewsSource ID {original_record_id} is not marked as extracted.",
                   task_name="tasks.scraper.hacker_news_tasks.extract_hn_story", task_args=[original_record_id])
        diagnosis.status_summary = "Pending: HackerNewsSource extraction."
        return diagnosis # Stop here if not extracted, as subsequent steps depend on it

    diagnosis.status_summary = "HackerNewsSource extracted."

    # Step 2: Check CompleteArticle
    # Assuming origin_id in CompleteArticle refers to HackerNewsSource.id
    # and source_id in CompleteArticle refers to HackerNewsSource.source_id (original SourceProvider.id)
    complete_article = db.query(CompleteArticle).filter(
        CompleteArticle.origin_id == hn_source.id,
        CompleteArticle.source_id == hn_source.source_id # Important to distinguish if multiple providers use HN
    ).first()

    if not complete_article:
        _add_issue(diagnosis.issues, "CompleteArticle Creation",
                   f"CompleteArticle not found for HackerNewsSource ID {original_record_id} (origin_id={hn_source.id}, source_id={hn_source.source_id}).",
                   task_name="tasks.scraper.hacker_news_tasks.extract_hn_story", task_args=[original_record_id])
        diagnosis.status_summary = "Pending: CompleteArticle creation."
        return diagnosis

    diagnosis.status_summary = "CompleteArticle created."
    ca_id = complete_article.id

    # Step 3: Check Summarization
    if not complete_article.summarized:
        _add_issue(diagnosis.issues, "Summarization",
                   f"CompleteArticle ID {ca_id} is not marked as summarized.",
                   task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
        diagnosis.status_summary = "Pending: Summarization."
        return diagnosis

    summarized_article = db.get(SummarizedArticle, ca_id) # ID is the same as CompleteArticle
    if not summarized_article:
        _add_issue(diagnosis.issues, "SummarizedArticle Creation",
                   f"SummarizedArticle record not found for ID {ca_id}, though CompleteArticle is marked summarized.",
                   task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id]) # Retrying summarize should recreate it
        diagnosis.status_summary = "Error: SummarizedArticle missing after summarization flag."
        return diagnosis

    diagnosis.status_summary = "Article summarized."
    sa_id = summarized_article.id # Define sa_id here, it's the same as ca_id

    # Step 4: Check Vectorization
    if not summarized_article.embedding_vector:
        _add_issue(diagnosis.issues, "Vectorization",
                   f"SummarizedArticle ID {sa_id} does not have an embedding vector.",
                   task_name="tasks.classify.summarize_tasks.vectorize_summarized_complete_article", task_args=[sa_id])
        diagnosis.status_summary = "Pending: Vectorization."
        return diagnosis

    diagnosis.status_summary = "Article vectorized."
    # sa_id is already defined above

    # Step 5 & 6: Check Distribution and Classification (ArticleProcessStatus)
    # This is more complex as it involves multiple topics (FilterCharacterSettings)
    # For now, let's just indicate it reached this stage. A more detailed check would query ArticleProcessStatus.
    # We need FilterCharacterSetting related to hn_source.source_id
    from app.db.models.filter import FilterCharacterSetting # Local import for now
    
    related_filters = db.query(FilterCharacterSetting).filter(
        FilterCharacterSetting.source_ids.contains([hn_source.source_id]) # Assuming source_ids is an array/JSON
    ).all()

    all_topics_processed = True
    if not related_filters:
        diagnosis.status_summary = "Article vectorized. No related topics (FilterCharacterSetting) found for this source provider."
        _add_issue(diagnosis.issues, "Topic Distribution", "No FilterCharacterSetting found for this source, so no distribution or classification happened.")
    else:
        for fc_setting in related_filters:
            aps = db.query(ArticleProcessStatus).filter(
                ArticleProcessStatus.article_id == sa_id,
                ArticleProcessStatus.topic_id == fc_setting.id
            ).first()

            if not aps:
                all_topics_processed = False
                _add_issue(diagnosis.issues, f"Distribution/Classification (Topic ID: {fc_setting.id})",
                           f"ArticleProcessStatus record not found for article ID {sa_id} and topic ID {fc_setting.id}.",
                           task_name="tasks.topic.topic_router_tasks.distribute_article", task_args=[sa_id, hn_source.source_id])
                # If APS is missing, distribute_article is the primary retry, it will then trigger classify.
                break # Stop checking further topics for this article if one APS is missing, suggest retrying distribute
            elif aps.status != ProcessStatus.success:
                all_topics_processed = False
                _add_issue(diagnosis.issues, f"Classification (Topic ID: {fc_setting.id})",
                           f"ArticleProcessStatus for article ID {sa_id}, topic ID {fc_setting.id} is '{aps.status.value}'.",
                           task_name="tasks.classify.classify_tasks.classify_summarized_article_for_topic", task_args=[sa_id, fc_setting.id])
                # If APS is not success, retry classification for this specific topic.
                # Consider resetting status to pending before retry:
                # db.query(ArticleProcessStatus).filter_by(id=aps.id).update({"status": ProcessStatus.pending})
                # db.commit()

        if all_topics_processed:
            diagnosis.status_summary = "Chain complete: Article processed for all relevant topics."
        elif not diagnosis.issues: # if all_topics_processed is False but no specific APS issue was added (e.g. distribute_article was suggested)
            diagnosis.status_summary = "Pending: Article distribution or classification for topics."


    # Only add "Completed" issue if no other issues were found.
    # The "Completed" pseudo-issue is just for single diagnosis display, not for scan results.
    if not diagnosis.issues: # Should check diagnosis.issues
         _add_issue(diagnosis.issues, "Completed", "All checks passed for this chain based on HackerNewsSource.")
         if diagnosis.status_summary == "Article vectorized." and not related_filters: # Corrected condition
             diagnosis.status_summary = "Article vectorized. No related topics found."
         elif all_topics_processed :
             diagnosis.status_summary = "Chain complete: Article processed for all relevant topics."
         # If all_topics_processed is false, status_summary is already set to "Pending: Article distribution or classification for topics."
         # or an issue was added that broke the loop.
    return diagnosis


@router.get("/admin/api/task-chain/diagnose/{source_type_path}/{original_record_id}", response_model=TaskChainDiagnosis)
async def diagnose_task_chain_api(
    source_type_path: SourceTypePath,
    original_record_id: int,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    if source_type_path == SourceTypePath.hackernews:
        return await _diagnose_hackernews_chain(original_record_id, db)
    elif source_type_path == SourceTypePath.rss:
        return await _diagnose_rss_chain(original_record_id, db)
    elif source_type_path == SourceTypePath.email:
        return await _diagnose_email_chain(original_record_id, db)
    else:
        raise HTTPException(status_code=400, detail=f"Diagnosis for source type '{source_type_path.value}' not implemented.")

async def _diagnose_rss_chain(original_rss_source_id: int, db: Session) -> TaskChainDiagnosis:
    diagnosis = TaskChainDiagnosis(
        original_record_id=original_rss_source_id,
        source_type=SourceTypePath.rss.value,
        status_summary="Starting RSS diagnosis...",
        issues=[]
    )

    # Step 1: Check RssSource record
    rss_source = db.get(RssSource, original_rss_source_id)
    if not rss_source:
        _add_issue(diagnosis.issues, "RssSource", f"RssSource record with ID {original_rss_source_id} not found.")
        diagnosis.status_summary = "Error: Original RssSource record not found."
        return diagnosis

    # Step 2: Check RssSource.extracted
    if not rss_source.extracted:
        _add_issue(diagnosis.issues, "RssSource Extraction",
                   f"RssSource ID {original_rss_source_id} is not marked as extracted.",
                   task_name="tasks.scraper.rss_tasks.extract_rss", task_args=[original_rss_source_id])
        diagnosis.status_summary = "Pending: RssSource extraction."
        return diagnosis
    diagnosis.status_summary = "RssSource extracted."

    # Step 3: Check CompleteArticle creation
    # origin_id in CompleteArticle refers to RssSource.id
    # source_id in CompleteArticle refers to RssSource.source_id (SourceProvider.id)
    complete_article = db.query(CompleteArticle).filter(
        CompleteArticle.origin_id == rss_source.id,
        CompleteArticle.source_id == rss_source.source_id
    ).first()

    if not complete_article:
        _add_issue(diagnosis.issues, "CompleteArticle Creation from RSS",
                   f"CompleteArticle not found for RssSource ID {original_rss_source_id} (origin_id={rss_source.id}, source_id={rss_source.source_id}).",
                   task_name="tasks.scraper.rss_tasks.extract_rss", task_args=[original_rss_source_id])
        diagnosis.status_summary = "Pending: CompleteArticle creation from RSS."
        return diagnosis
    diagnosis.status_summary = "CompleteArticle created."
    ca_id = complete_article.id

    # Step 4: Check Summarization (CompleteArticle.summarized)
    if not complete_article.summarized:
        _add_issue(diagnosis.issues, "Summarization",
                   f"CompleteArticle ID {ca_id} is not marked as summarized.",
                   task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
        diagnosis.status_summary = "Pending: Summarization."
        return diagnosis

    # Step 5: Check SummarizedArticle creation
    summarized_article = db.get(SummarizedArticle, ca_id) # ID is the same as CompleteArticle
    if not summarized_article:
        _add_issue(diagnosis.issues, "SummarizedArticle Creation",
                   f"SummarizedArticle record not found for ID {ca_id}, though CompleteArticle is marked summarized.",
                   task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
        diagnosis.status_summary = "Error: SummarizedArticle missing after summarization flag."
        return diagnosis
    diagnosis.status_summary = "Article summarized."
    sa_id = summarized_article.id

    # Step 6: Check Vectorization (SummarizedArticle.embedding_vector)
    if not summarized_article.embedding_vector: # Assuming embedding_vector stores the actual vector or a path
        _add_issue(diagnosis.issues, "Vectorization",
                   f"SummarizedArticle ID {sa_id} does not have an embedding vector.",
                   task_name="tasks.classify.summarize_tasks.vectorize_summarized_complete_article", task_args=[sa_id])
        diagnosis.status_summary = "Pending: Vectorization."
        return diagnosis
    diagnosis.status_summary = "Article vectorized."

    # Step 7: Check Distribution and Classification (ArticleProcessStatus)
    from app.db.models.filter import FilterCharacterSetting # Local import
    
    related_filters = db.query(FilterCharacterSetting).filter(
        FilterCharacterSetting.source_ids.contains([rss_source.source_id]) # Check against SourceProvider.id
    ).all()

    all_topics_processed = True
    if not related_filters:
        diagnosis.status_summary = "Article vectorized. No related topics (FilterCharacterSetting) found for this source provider."
        _add_issue(diagnosis.issues, "Topic Distribution", "No FilterCharacterSetting found for this source's provider, so no distribution or classification happened.")
    else:
        for fc_setting in related_filters:
            aps = db.query(ArticleProcessStatus).filter(
                ArticleProcessStatus.article_id == sa_id,
                ArticleProcessStatus.topic_id == fc_setting.id
            ).first()

            if not aps:
                all_topics_processed = False
                _add_issue(diagnosis.issues, f"Distribution/Classification (Topic ID: {fc_setting.id})",
                           f"ArticleProcessStatus record not found for article ID {sa_id} and topic ID {fc_setting.id}.",
                           task_name="tasks.topic.topic_router_tasks.distribute_article", task_args=[sa_id, rss_source.source_id])
                break 
            elif aps.status != ProcessStatus.success:
                all_topics_processed = False
                _add_issue(diagnosis.issues, f"Classification (Topic ID: {fc_setting.id})",
                           f"ArticleProcessStatus for article ID {sa_id}, topic ID {fc_setting.id} is '{aps.status.value}'.",
                           task_name="tasks.classify.classify_tasks.classify_summarized_article_for_topic", task_args=[sa_id, fc_setting.id])
        
        if all_topics_processed:
            diagnosis.status_summary = "Chain complete: Article processed for all relevant topics."
        elif not diagnosis.issues:
            diagnosis.status_summary = "Pending: Article distribution or classification for topics."

    if not diagnosis.issues:
         _add_issue(diagnosis.issues, "Completed", "All checks passed for this RSS chain.")
         if diagnosis.status_summary == "Article vectorized." and not related_filters:
             diagnosis.status_summary = "Article vectorized. No related topics found."
         elif all_topics_processed: # Ensure this condition is met for completion status
             diagnosis.status_summary = "Chain complete: Article processed for all relevant topics."
    return diagnosis

async def _diagnose_email_chain(original_email_source_id: int, db: Session) -> TaskChainDiagnosis:
    diagnosis = TaskChainDiagnosis(
        original_record_id=original_email_source_id,
        source_type=SourceTypePath.email.value,
        status_summary="Starting Email diagnosis...",
        issues=[]
    )

    # Step 1: Check EmailSource record
    email_source = db.get(EmailSource, original_email_source_id)
    if not email_source:
        _add_issue(diagnosis.issues, "EmailSource", f"EmailSource record with ID {original_email_source_id} not found.")
        diagnosis.status_summary = "Error: Original EmailSource record not found."
        return diagnosis

    # Step 2: Check EmailSourceFailedToExtract (early failure)
    failed_extraction_record = db.get(EmailSourceFailedToExtract, original_email_source_id)
    if failed_extraction_record:
        _add_issue(diagnosis.issues, "EmailSource Early Extraction Failure",
                   f"EmailSource ID {original_email_source_id} recorded in EmailSourceFailedToExtract.",
                   task_name="tasks.scraper.email_tasks.extract_email_content", task_args=[original_email_source_id])
        diagnosis.status_summary = "Error: EmailSource failed during initial HTML to MD conversion or pre-LLM processing."
        return diagnosis
        
    # Step 3: Check EmailSource.extracted
    if not email_source.extracted:
        _add_issue(diagnosis.issues, "EmailSource Extraction",
                   f"EmailSource ID {original_email_source_id} is not marked as extracted.",
                   task_name="tasks.scraper.email_tasks.extract_email_content", task_args=[original_email_source_id])
        diagnosis.status_summary = "Pending: EmailSource extraction (LLM processing, etc.)."
        return diagnosis
    diagnosis.status_summary = "EmailSource extracted (initial processing done)."

    # Step 4: Determine if it was processed as a direct CompleteArticle or as IncompleteArticles (Newsletter)
    # Check for directly created CompleteArticle
    direct_complete_article = db.query(CompleteArticle).filter(
        CompleteArticle.origin_id == email_source.id,
        CompleteArticle.source_id == email_source.source_id # Ensure it's from this specific EmailSource processing
    ).first()

    if direct_complete_article:
        # Path A: Non-Newsletter (or failed newsletter detection)
        diagnosis.status_summary += " Processed as direct article."
        ca_id = direct_complete_article.id

        # Check Summarization
        if not direct_complete_article.summarized:
            _add_issue(diagnosis.issues, "Summarization (Direct Email)",
                       f"CompleteArticle ID {ca_id} (from EmailSource {original_email_source_id}) is not marked as summarized.",
                       task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
            diagnosis.status_summary = "Pending: Summarization for direct email article."
            return diagnosis

        summarized_article = db.get(SummarizedArticle, ca_id)
        if not summarized_article:
            _add_issue(diagnosis.issues, "SummarizedArticle Creation (Direct Email)",
                       f"SummarizedArticle record not found for ID {ca_id}.",
                       task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
            diagnosis.status_summary = "Error: SummarizedArticle missing for direct email article."
            return diagnosis
        diagnosis.status_summary = "Direct email article summarized."
        sa_id = summarized_article.id

        # Check Vectorization
        if not summarized_article.embedding_vector:
            _add_issue(diagnosis.issues, "Vectorization (Direct Email)",
                       f"SummarizedArticle ID {sa_id} does not have an embedding vector.",
                       task_name="tasks.classify.summarize_tasks.vectorize_summarized_complete_article", task_args=[sa_id])
            diagnosis.status_summary = "Pending: Vectorization for direct email article."
            return diagnosis
        diagnosis.status_summary = "Direct email article vectorized."

        # Check Distribution and Classification
        from app.db.models.filter import FilterCharacterSetting # Local import
        related_filters = db.query(FilterCharacterSetting).filter(
            FilterCharacterSetting.source_ids.contains([email_source.source_id])
        ).all()
        all_topics_processed = True
        if not related_filters:
            diagnosis.status_summary += " No related topics found."
            _add_issue(diagnosis.issues, "Topic Distribution (Direct Email)", "No FilterCharacterSetting found.")
        else:
            for fc_setting in related_filters:
                aps = db.query(ArticleProcessStatus).filter(
                    ArticleProcessStatus.article_id == sa_id,
                    ArticleProcessStatus.topic_id == fc_setting.id
                ).first()
                if not aps:
                    all_topics_processed = False
                    _add_issue(diagnosis.issues, f"Distribution/Classification (Topic ID: {fc_setting.id}, Direct Email)",
                               f"ArticleProcessStatus record not found for article ID {sa_id}, topic ID {fc_setting.id}.",
                               task_name="tasks.topic.topic_router_tasks.distribute_article", task_args=[sa_id, email_source.source_id])
                    break
                elif aps.status != ProcessStatus.success:
                    all_topics_processed = False
                    _add_issue(diagnosis.issues, f"Classification (Topic ID: {fc_setting.id}, Direct Email)",
                               f"ArticleProcessStatus for article ID {sa_id}, topic ID {fc_setting.id} is '{aps.status.value}'.",
                               task_name="tasks.classify.classify_tasks.classify_summarized_article_for_topic", task_args=[sa_id, fc_setting.id])
            if all_topics_processed:
                diagnosis.status_summary = "Chain complete: Direct email article processed for all relevant topics."
            elif not diagnosis.issues : # Some issue in APS loop already added.
                 diagnosis.status_summary = "Pending: Direct email article distribution or classification."
    else:
        # Path B: Newsletter - check for IncompleteArticles
        incomplete_articles = db.query(IncompleteArticle).filter(
            IncompleteArticle.origin_id == email_source.id, # origin_id of IncompleteArticle points to EmailSource.id
            IncompleteArticle.source_id == email_source.source_id
        ).all()

        if not incomplete_articles:
            # This case implies email_source.extracted is True, but neither DirectCompleteArticle nor IncompleteArticles were created.
            # This could mean the LLM call in _extract_email_content failed or returned no articles for a newsletter.
            _add_issue(diagnosis.issues, "Article Extraction from Email",
                       f"EmailSource ID {original_email_source_id} was extracted, but no CompleteArticle or IncompleteArticles found. LLM processing might have failed or deemed it not a newsletter with no articles.",
                       task_name="tasks.scraper.email_tasks.extract_email_content", task_args=[original_email_source_id])
            diagnosis.status_summary = "Error: Failed to create articles from extracted email content."
            return diagnosis

        diagnosis.status_summary += f" Processed as newsletter with {len(incomplete_articles)} item(s)."
        all_newsletter_items_processed = True

        for ia_idx, ia in enumerate(incomplete_articles):
            item_status_prefix = f"Newsletter Item {ia_idx+1} (IA_ID: {ia.id}): "
            
            if not ia.expanded:
                all_newsletter_items_processed = False
                _add_issue(diagnosis.issues, f"{item_status_prefix}Expansion",
                           f"IncompleteArticle ID {ia.id} is not marked as expanded.",
                           task_name="tasks.scraper.blurb_tasks.fetch_incomplete_article", task_args=[ia.id])
                diagnosis.status_summary = f"Pending: Expansion of newsletter item IA_ID {ia.id}"
                return diagnosis # Stop at first unexpanded item

            # Check for CompleteArticle from this IncompleteArticle
            # Here, CompleteArticle.origin_id refers to IncompleteArticle.id
            ca_from_ia = db.query(CompleteArticle).filter(
                CompleteArticle.origin_id == ia.id,
                CompleteArticle.source_id == ia.source_id 
            ).first()

            if not ca_from_ia:
                all_newsletter_items_processed = False
                _add_issue(diagnosis.issues, f"{item_status_prefix}CompleteArticle Creation",
                           f"CompleteArticle not found for IncompleteArticle ID {ia.id}.",
                           task_name="tasks.scraper.blurb_tasks.fetch_incomplete_article", task_args=[ia.id])
                diagnosis.status_summary = f"Pending: Creation of CompleteArticle from newsletter item IA_ID {ia.id}"
                return diagnosis
            
            ca_id = ca_from_ia.id
            current_item_summary = f"{item_status_prefix}CompleteArticle ID {ca_id} created."

            # Check Summarization for this item's CompleteArticle
            if not ca_from_ia.summarized:
                all_newsletter_items_processed = False
                _add_issue(diagnosis.issues, f"{item_status_prefix}Summarization",
                           f"CompleteArticle ID {ca_id} is not marked as summarized.",
                           task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
                diagnosis.status_summary = f"Pending: Summarization for newsletter item CA_ID {ca_id}"
                return diagnosis
            
            summarized_article_from_ia = db.get(SummarizedArticle, ca_id)
            if not summarized_article_from_ia:
                all_newsletter_items_processed = False
                _add_issue(diagnosis.issues, f"{item_status_prefix}SummarizedArticle Creation",
                           f"SummarizedArticle record not found for ID {ca_id}.",
                           task_name="tasks.classify.summarize_tasks.summarize_complete_article", task_args=[ca_id])
                diagnosis.status_summary = f"Error: SummarizedArticle missing for newsletter item CA_ID {ca_id}"
                return diagnosis
            current_item_summary += " Summarized."
            sa_id = summarized_article_from_ia.id

            # Check Vectorization
            if not summarized_article_from_ia.embedding_vector:
                all_newsletter_items_processed = False
                _add_issue(diagnosis.issues, f"{item_status_prefix}Vectorization",
                           f"SummarizedArticle ID {sa_id} does not have an embedding vector.",
                           task_name="tasks.classify.summarize_tasks.vectorize_summarized_complete_article", task_args=[sa_id])
                diagnosis.status_summary = f"Pending: Vectorization for newsletter item SA_ID {sa_id}"
                return diagnosis
            current_item_summary += " Vectorized."
            
            # Check Distribution and Classification for this item
            from app.db.models.filter import FilterCharacterSetting # Local import
            # Note: related_filters uses email_source.source_id (the SourceProvider id for the email itself)
            # This assumes all articles from a newsletter are distributed based on the newsletter's source provider settings.
            related_filters_for_item = db.query(FilterCharacterSetting).filter(
                FilterCharacterSetting.source_ids.contains([email_source.source_id]) 
            ).all()

            item_all_topics_processed = True
            if not related_filters_for_item:
                current_item_summary += " No related topics."
                _add_issue(diagnosis.issues, f"{item_status_prefix}Topic Distribution", "No FilterCharacterSetting found for this item's source provider.")
            else:
                for fc_setting in related_filters_for_item:
                    aps = db.query(ArticleProcessStatus).filter(
                        ArticleProcessStatus.article_id == sa_id,
                        ArticleProcessStatus.topic_id == fc_setting.id
                    ).first()
                    if not aps:
                        item_all_topics_processed = False
                        _add_issue(diagnosis.issues, f"{item_status_prefix}Distribution/Classification (Topic ID: {fc_setting.id})",
                                   f"ArticleProcessStatus record not found for article ID {sa_id}, topic ID {fc_setting.id}.",
                                   # distribute_article takes summarized_article_id and the original_provider_id (EmailSource.source_id)
                                   task_name="tasks.topic.topic_router_tasks.distribute_article", task_args=[sa_id, email_source.source_id]) 
                        break 
                    elif aps.status != ProcessStatus.success:
                        item_all_topics_processed = False
                        _add_issue(diagnosis.issues, f"{item_status_prefix}Classification (Topic ID: {fc_setting.id})",
                                   f"ArticleProcessStatus for article ID {sa_id}, topic ID {fc_setting.id} is '{aps.status.value}'.",
                                   task_name="tasks.classify.classify_tasks.classify_summarized_article_for_topic", task_args=[sa_id, fc_setting.id])
                
                if item_all_topics_processed:
                    current_item_summary += " Processed for all topics."
                elif not any(issue.step_name.startswith(item_status_prefix) for issue in diagnosis.issues): # check if an issue was already added for this item in the APS loop
                    current_item_summary += " Pending distribution/classification."


            if not item_all_topics_processed:
                all_newsletter_items_processed = False
                diagnosis.status_summary = current_item_summary # Update overall summary to the failing item's status
                # An issue for this item was already added, so we can just return to show the first failure.
                return diagnosis 
            
            # If this item is fine, update overall status and continue to next item
            diagnosis.status_summary = current_item_summary


        if all_newsletter_items_processed:
            diagnosis.status_summary = "Chain complete: All newsletter items processed."

    if not diagnosis.issues:
         _add_issue(diagnosis.issues, "Completed", "All checks passed for this Email chain.")
         # Final status_summary should already be set correctly by the logic above.
         if diagnosis.status_summary.startswith("Starting Email diagnosis..."): # Should not happen if logic is correct
             diagnosis.status_summary = "Chain checks appear complete, but final status unclear."

    return diagnosis

async def _get_user_email_from_source_provider_id(db: Session, source_provider_id: int) -> Optional[str]:
    user_source = db.query(UserSource).filter(UserSource.inner_source_id == source_provider_id).first()
    if user_source:
        user = db.get(User, user_source.user_id)
        if user and user.email is not None:
            return str(user.email) # Ensure it's a string
    return None

async def _scan_hackernews_for_issues(db: Session, limit: Optional[int] = 100) -> List[ProblematicChainInfo]:
    problematic_chains: List[ProblematicChainInfo] = []
    
    # Query for HackerNewsSource IDs, potentially with ordering or filtering
    # For now, let's take the most recent ones or a sample if the table is large.
    # Adding order_by HackerNewsSource.id.desc() to get recent ones if ID is auto-incrementing.
    # The limit is to prevent scanning a huge table in one go.
    hn_sources_to_scan = db.query(HackerNewsSource).order_by(HackerNewsSource.id.desc()).limit(limit).all()

    for hn_source in hn_sources_to_scan:
        # We need to pass a new list for issues each time to _diagnose_hackernews_chain
        diagnosis_result = await _diagnose_hackernews_chain(hn_source.id, db)
        
        # Check if there are actual issues (not the "Completed" pseudo-issue)
        actual_issues = [issue for issue in diagnosis_result.issues if issue.step_name != "Completed"]
        
        if actual_issues:
            problematic_chains.append(ProblematicChainInfo(
                original_record_id=hn_source.id,
                status_summary=diagnosis_result.status_summary,
                first_issue_step=actual_issues[0].step_name if actual_issues else "N/A",
                first_issue_description=actual_issues[0].issue_description if actual_issues else "N/A"
            ))
    return problematic_chains

@router.get("/admin/api/task-chain/scan/{source_type_path}", response_model=List[ProblematicChainInfo])
async def scan_task_chains_api(
    source_type_path: SourceTypePath,
    limit: Optional[int] = Query(100, ge=10, le=500), # Add limit parameter with validation
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    if source_type_path == SourceTypePath.hackernews:
        return await _scan_hackernews_for_issues(db, limit=limit)
    elif source_type_path == SourceTypePath.rss:
        return await _scan_rss_for_issues(db, limit=limit)
    elif source_type_path == SourceTypePath.email:
        return await _scan_email_for_issues(db, limit=limit)
    else:
        raise HTTPException(status_code=400, detail=f"Scanning for source type '{source_type_path.value}' not implemented.")

async def _scan_rss_for_issues(db: Session, limit: Optional[int] = 100) -> List[ProblematicChainInfo]:
    problematic_chains: List[ProblematicChainInfo] = []
    logger.info(f"Scanning for RSS issues (limit: {limit})")

    # Query for RssSource records that are not extracted
    # or where the corresponding CompleteArticle is not summarized
    # or where the SummarizedArticle has no embedding vector
    # or where ArticleProcessStatus indicates failure or is missing.
    # This is a simplified scan; a more exhaustive scan might join tables more explicitly.

    # Case 1: RssSource not extracted
    unextracted_rss = db.query(RssSource).filter(RssSource.extracted == False)\
        .order_by(RssSource.id.desc()).limit(limit).all()

    # Case 2: Extracted, but CompleteArticle not summarized
    # We need to join RssSource with CompleteArticle
    # and then filter where RssSource.extracted = True and CompleteArticle.summarized = False
    unsummarized_ca_from_rss = db.query(RssSource)\
        .join(CompleteArticle, (CompleteArticle.origin_id == RssSource.id) & (CompleteArticle.source_id == RssSource.source_id))\
        .filter(RssSource.extracted == True, CompleteArticle.summarized == False)\
        .order_by(RssSource.id.desc()).limit(limit).all()

    # Combine and de-duplicate potential RssSource records to diagnose
    # (Using a dictionary to ensure uniqueness by RssSource.id)
    rss_sources_map = {rs.id: rs for rs in unextracted_rss}
    for rs in unsummarized_ca_from_rss:
        if rs.id not in rss_sources_map:
            rss_sources_map[rs.id] = rs
    
    # Limit the total number of sources to diagnose in this run to 'limit'
    # Sort by ID to maintain some consistency if needed, though map doesn't guarantee order
    # Here, taking the first 'limit' items by their original ID order (desc) is tricky after map.
    # A better approach might be to run separate limited queries and merge results,
    # or to construct a more complex single query if performance allows.
    # For now, we will process the combined list up to a reasonable cap if it exceeds 'limit'.
    
    rss_sources_to_diagnose = list(rss_sources_map.values())
    # Guard against limit being None, though Query should provide a default
    current_limit = limit if limit is not None else 100 
    if len(rss_sources_to_diagnose) > current_limit * 2 : # Heuristic cap
         logger.warning(f"Combined RSS sources to diagnose ({len(rss_sources_to_diagnose)}) exceeds practical limit, consider refining scan queries.")
         # Potentially truncate or select a subset, but for now, process what's gathered.


    processed_ids = set() # To avoid re-diagnosing if a source appears in multiple problematic queries.

    for rss_source in rss_sources_to_diagnose:
        if rss_source.id in processed_ids:
            continue
        processed_ids.add(rss_source.id)
        # Ensure limit is not None for comparison
        if limit is not None and len(problematic_chains) >= limit: # Stop if we have enough problematic chains for this scan run
            break

        diagnosis_result = await _diagnose_rss_chain(rss_source.id, db)
        actual_issues = [issue for issue in diagnosis_result.issues if issue.step_name != "Completed"]
        
        if actual_issues:
            user_email_str = await _get_user_email_from_source_provider_id(db, rss_source.source_id)
            user_info = f" (User: {user_email_str})" if user_email_str else ""
            
            problematic_chains.append(ProblematicChainInfo(
                original_record_id=rss_source.id,
                status_summary=f"{diagnosis_result.status_summary}{user_info}",
                first_issue_step=actual_issues[0].step_name,
                first_issue_description=actual_issues[0].issue_description
            ))

    if not problematic_chains and (unextracted_rss or unsummarized_ca_from_rss) :
         logger.info("RSS scan ran and found potential items, but diagnosis yielded no actionable issues currently (or all were 'Completed').")
    elif not unextracted_rss and not unsummarized_ca_from_rss:
        logger.info("RSS scan ran, no RssSource records matched the initial problematic criteria.")
        
    return problematic_chains

async def _scan_email_for_issues(db: Session, limit: Optional[int] = 100) -> List[ProblematicChainInfo]:
    problematic_chains: List[ProblematicChainInfo] = []
    logger.info(f"Scanning for Email issues (limit: {limit})")

    # Case 1: EmailSource not extracted
    unextracted_emails = db.query(EmailSource).filter(EmailSource.extracted == False)\
        .order_by(EmailSource.id.desc()).limit(limit).all()

    # Case 2: EmailSource failed early (in EmailSourceFailedToExtract)
    failed_early_emails_ids_query = select(EmailSourceFailedToExtract.id).limit(limit)
    failed_early_emails_ids = db.execute(failed_early_emails_ids_query).scalars().all()
    failed_early_emails = []
    if failed_early_emails_ids:
        failed_early_emails = db.query(EmailSource).filter(EmailSource.id.in_(failed_early_emails_ids)).all()
        
    # Case 3: Extracted, but direct CompleteArticle not summarized
    unsummarized_direct_ca_from_email = db.query(EmailSource)\
        .join(CompleteArticle, (CompleteArticle.origin_id == EmailSource.id) & (CompleteArticle.source_id == EmailSource.source_id))\
        .outerjoin(IncompleteArticle, (IncompleteArticle.origin_id == EmailSource.id) & (IncompleteArticle.source_id == EmailSource.source_id))\
        .filter(EmailSource.extracted == True, CompleteArticle.summarized == False, IncompleteArticle.id == None)\
        .order_by(EmailSource.id.desc()).limit(limit).all() # Ensure it's not a newsletter path

    # Case 4: Newsletter: IncompleteArticle not expanded
    unexpanded_ia_emails = db.query(EmailSource)\
        .join(IncompleteArticle, (IncompleteArticle.origin_id == EmailSource.id) & (IncompleteArticle.source_id == EmailSource.source_id))\
        .filter(EmailSource.extracted == True, IncompleteArticle.expanded == False)\
        .order_by(EmailSource.id.desc()).limit(limit).all()
        
    # Case 5: Newsletter: IncompleteArticle expanded, but its CompleteArticle not summarized
    unsummarized_ca_from_ia_emails = db.query(EmailSource)\
        .join(IncompleteArticle, (IncompleteArticle.origin_id == EmailSource.id) & (IncompleteArticle.source_id == EmailSource.source_id))\
        .join(CompleteArticle, (CompleteArticle.origin_id == IncompleteArticle.id) & (CompleteArticle.source_id == IncompleteArticle.source_id))\
        .filter(EmailSource.extracted == True, IncompleteArticle.expanded == True, CompleteArticle.summarized == False)\
        .order_by(EmailSource.id.desc()).limit(limit).all()

    email_sources_map = {}
    for es_list in [unextracted_emails, failed_early_emails, unsummarized_direct_ca_from_email, unexpanded_ia_emails, unsummarized_ca_from_ia_emails]:
        for es in es_list:
            if es.id not in email_sources_map:
                email_sources_map[es.id] = es
    
    email_sources_to_diagnose = list(email_sources_map.values())
    # Guard against limit being None
    current_email_limit = limit if limit is not None else 100
    if len(email_sources_to_diagnose) > current_email_limit * 3 : # Heuristic cap for combined email sources
         logger.warning(f"Combined Email sources to diagnose ({len(email_sources_to_diagnose)}) exceeds practical limit.")

    processed_ids = set()
    for email_source in email_sources_to_diagnose:
        if email_source.id in processed_ids:
            continue
        processed_ids.add(email_source.id)
        # Ensure limit is not None for comparison
        if limit is not None and len(problematic_chains) >= limit:
            break
            
        diagnosis_result = await _diagnose_email_chain(email_source.id, db)
        actual_issues = [issue for issue in diagnosis_result.issues if issue.step_name != "Completed"]
        
        if actual_issues:
            user_email_str = await _get_user_email_from_source_provider_id(db, email_source.source_id)
            user_info = f" (User: {user_email_str})" if user_email_str else ""

            problematic_chains.append(ProblematicChainInfo(
                original_record_id=email_source.id,
                status_summary=f"{diagnosis_result.status_summary}{user_info}",
                first_issue_step=actual_issues[0].step_name,
                first_issue_description=actual_issues[0].issue_description
            ))
            
    if not problematic_chains and email_sources_to_diagnose :
         logger.info("Email scan ran and found potential items, but diagnosis yielded no actionable issues currently.")
    elif not email_sources_to_diagnose:
        logger.info("Email scan ran, no EmailSource records matched the initial problematic criteria.")
        
    return problematic_chains

class RetryTaskRequest(schemas.BaseModel):
    task_name: str
    task_args: Optional[List] = None
    task_kwargs: Optional[dict] = None

@router.post("/admin/api/task-chain/retry", response_model=schemas.Message) # Assuming app.schemas has a Message schema
async def retry_task_chain_api(
    payload: RetryTaskRequest,
    current_admin: models.User = Depends(get_current_admin_user),
):
    celery_app_instance = get_celery_app()
    if not celery_app_instance:
        raise HTTPException(status_code=500, detail="Celery app not available.")

    logger.info(f"Admin attempting to retry task: '{payload.task_name}'")

    try:
        logger.info(f"Admin trigger retry for task: {payload.task_name} with args: {payload.task_args}, kwargs: {payload.task_kwargs}")
        celery_app_instance.send_task(
            payload.task_name,
            args=payload.task_args or [],
            kwargs=payload.task_kwargs or {}
        )
        return schemas.Message(message=f"Successfully queued task '{payload.task_name}' for retry.")
    except Exception as e:
        logger.error(f"Error queueing task {payload.task_name} for retry: {e}")
        raise HTTPException(status_code=500, detail=f"Error queueing task for retry: {str(e)}")


def get_celery_app():
    """延迟导入celery_app以避免启动时的导入冲突"""
    try:
        from tasks.celery_app import celery_app
        return celery_app
    except Exception as e:
        logger.error(f"Failed to import celery_app: {e}")
        return None


def get_failed_tasks(db: Session, page: int = 1, page_size: int = 20):
    """获取失败的Celery任务"""
    offset = (page - 1) * page_size

    # 查询失败的任务
    query = text("""
        SELECT
            id,
            task_id,
            status,
            result,
            date_done,
            traceback,
            name,
            args,
            kwargs,
            worker,
            retries,
            queue
        FROM feeds.celery_taskmeta
        WHERE status = 'FAILURE'
        ORDER BY date_done DESC
        LIMIT :limit OFFSET :offset
    """)

    result = db.execute(query, {"limit": page_size, "offset": offset})
    tasks = result.fetchall()

    # 获取总数
    count_query = text("SELECT COUNT(*) FROM feeds.celery_taskmeta WHERE status = 'FAILURE'")
    total_count = db.execute(count_query).scalar()

    # 处理任务数据
    processed_tasks = []
    for task in tasks:
        try:
            # 解析args和kwargs
            args = []
            kwargs = {}
            if task.args:
                try:
                    args = pickle.loads(task.args)
                except:
                    args = []

            if task.kwargs:
                try:
                    kwargs = pickle.loads(task.kwargs)
                except:
                    kwargs = {}

            # 解析result (错误信息)
            error_message = ""
            error_type = ""
            if task.result:
                try:
                    result_data = pickle.loads(task.result)
                    if isinstance(result_data, dict):
                        if 'exc_message' in result_data:
                            error_message = str(result_data['exc_message'])
                        elif len(result_data) > 0:
                            error_message = str(result_data)
                        if 'exc_type' in result_data:
                            error_type = str(result_data['exc_type'])
                    else:
                        error_message = str(result_data)
                except:
                    error_message = "Failed to parse error message"

            processed_tasks.append({
                'id': task.id,
                'task_id': task.task_id,
                'status': task.status,
                'name': task.name or 'Unknown',
                'args': args,
                'kwargs': kwargs,
                'error_message': error_message,
                'error_type': error_type,
                'traceback': task.traceback,
                'date_done': task.date_done,
                'worker': task.worker,
                'retries': task.retries or 0,
                'queue': task.queue
            })
        except Exception as e:
            logger.error(f"Error processing task {task.task_id}: {e}")
            continue

    return processed_tasks, total_count


@router.get("/admin/celery-tasks", response_class=HTMLResponse)
async def celery_tasks_page(
    request: Request,
    page: int = Query(1, ge=1),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Celery任务管理页面"""
    page_size = 20
    failed_tasks, total_count = get_failed_tasks(db, page, page_size)

    total_pages = ((total_count or 0) + page_size - 1) // page_size

    return templates.TemplateResponse(
        "admin/celery_tasks.html",
        get_base_context(
            request,
            current_user=current_admin,
            failed_tasks=failed_tasks,
            current_page=page,
            total_pages=total_pages,
            total_count=total_count,
            page_size=page_size
        )
    )


@router.post("/admin/celery-tasks/retry")
async def retry_tasks(
    request: Request,
    task_ids: List[str] = Form(...),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """批量重试失败的任务"""
    if not task_ids:
        raise HTTPException(status_code=400, detail="No tasks selected")

    success_count = 0
    error_count = 0
    errors = []

    for task_id in task_ids:
        try:
            # 获取原始任务信息
            query = text("""
                SELECT name, args, kwargs
                FROM feeds.celery_taskmeta
                WHERE task_id = :task_id AND status = 'FAILURE'
            """)
            result = db.execute(query, {"task_id": task_id}).fetchone()

            if not result:
                errors.append(f"Task {task_id} not found or not failed")
                error_count += 1
                continue

            # 解析参数
            args = []
            kwargs = {}

            if result.args:
                try:
                    args = pickle.loads(result.args)
                except:
                    args = []

            if result.kwargs:
                try:
                    kwargs = pickle.loads(result.kwargs)
                except:
                    kwargs = {}

            # 重新提交任务
            task_name = result.name
            logger.info(f"Attempting to retry task: {task_name} with ID: {task_id}")

            if task_name and task_name.strip():
                # 获取任务函数
                celery_app = get_celery_app()
                if celery_app:
                    logger.info(f"Celery app available. Registered tasks: {list(celery_app.tasks.keys())}")
                    task_func = celery_app.tasks.get(task_name)
                    if task_func:
                        logger.info(f"Found task function for {task_name}, submitting with args={args}, kwargs={kwargs}")
                        task_func.delay(*args, **kwargs)
                        success_count += 1
                        logger.info(f"Successfully retried task {task_name} with ID {task_id}")
                    else:
                        error_msg = f"Task function '{task_name}' not found in registered tasks: {list(celery_app.tasks.keys())}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                        error_count += 1
                else:
                    error_msg = f"Celery app not available"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    error_count += 1
            else:
                error_msg = f"Task {task_id} has no name (NULL/empty) - cannot retry without task name. This is a Celery configuration issue."
                logger.error(error_msg)
                errors.append(error_msg)
                error_count += 1

        except Exception as e:
            logger.error(f"Error retrying task {task_id}: {e}")
            errors.append(f"Error retrying task {task_id}: {str(e)}")
            error_count += 1

    # 返回结果页面或重定向
    if error_count == 0:
        message = f"Successfully retried {success_count} tasks"
    else:
        message = f"Retried {success_count} tasks, {error_count} errors occurred"
        # Log all errors for debugging
        logger.error(f"Retry errors: {errors}")

    # 重定向回任务列表页面，带上消息
    response = RedirectResponse(
        url=f"/admin/celery-tasks?message={message}",
        status_code=303
    )
    return response


@router.get("/admin/task-chain-retry", response_class=HTMLResponse)
async def task_chain_retry_page(
    request: Request,
    current_admin: models.User = Depends(get_current_admin_user)
):
    """页面用于诊断和重试任务链"""
    return templates.TemplateResponse(
        "admin/task_chain_retry.html",
        get_base_context(request, current_user=current_admin)
    )


@router.get("/admin/celery-tasks/{task_id}/detail", response_class=HTMLResponse)
async def task_detail(
    request: Request,
    task_id: str,
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """查看任务详情"""
    query = text("""
        SELECT
            id, task_id, status, result, date_done, traceback, name,
            args, kwargs, worker, retries, queue
        FROM feeds.celery_taskmeta
        WHERE task_id = :task_id
    """)

    result = db.execute(query, {"task_id": task_id}).fetchone()

    if not result:
        raise HTTPException(status_code=404, detail="Task not found")

    # 解析任务数据
    try:
        args = pickle.loads(result.args) if result.args else []
    except:
        args = []

    try:
        kwargs = pickle.loads(result.kwargs) if result.kwargs else {}
    except:
        kwargs = {}

    try:
        if result.result:
            error_data = pickle.loads(result.result)
            if isinstance(error_data, dict):
                error_message = error_data.get('exc_message', str(error_data))
                error_type = error_data.get('exc_type', 'Unknown')
            else:
                error_message = str(error_data)
                error_type = 'Unknown'
        else:
            error_message = ""
            error_type = ""
    except:
        error_message = "Failed to parse error data"
        error_type = "ParseError"

    task_detail = {
        'id': result.id,
        'task_id': result.task_id,
        'status': result.status,
        'name': result.name,
        'args': args,
        'kwargs': kwargs,
        'error_message': error_message,
        'error_type': error_type,
        'traceback': result.traceback,
        'date_done': result.date_done,
        'worker': result.worker,
        'retries': result.retries or 0,
        'queue': result.queue
    }

    return templates.TemplateResponse(
        "admin/celery_task_detail.html",
        get_base_context(
            request,
            current_user=current_admin,
            task=task_detail
        )
    )


@router.post("/admin/celery-tasks/cleanup")
async def cleanup_failed_tasks(
    request: Request,
    days_old: int = Form(7),
    current_admin: models.User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """清理指定天数之前的失败任务"""
    try:
        # 删除指定天数之前的失败任务
        cleanup_query = text("""
            DELETE FROM feeds.celery_taskmeta
            WHERE status = 'FAILURE'
            AND date_done < NOW() - INTERVAL :days DAY
        """)

        # 先计算要删除的数量
        count_query = text("""
            SELECT COUNT(*) FROM feeds.celery_taskmeta
            WHERE status = 'FAILURE'
            AND date_done < NOW() - INTERVAL :days DAY
        """)
        deleted_count = db.execute(count_query, {"days": days_old}).scalar()

        # 执行删除
        db.execute(cleanup_query, {"days": days_old})
        db.commit()

        logger.info(f"Cleaned up {deleted_count} failed tasks older than {days_old} days")

        message = f"Successfully cleaned up {deleted_count} failed tasks older than {days_old} days"

    except Exception as e:
        logger.error(f"Error cleaning up failed tasks: {e}")
        message = f"Error cleaning up failed tasks: {str(e)}"

    # 重定向回任务列表页面
    response = RedirectResponse(
        url=f"/admin/celery-tasks?message={message}",
        status_code=303
    )
    return response
