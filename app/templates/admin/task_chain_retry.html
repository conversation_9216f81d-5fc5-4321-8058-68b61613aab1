{% extends "admin/layout.html" %} {% block title %}Task Chain Diagnosis &
Retry{% endblock %} {% block content %}
<div class="container mt-4">
  <h2>Task Chain Diagnosis & Retry</h2>
  <p>
    Diagnose and retry failed Celery task chains starting from an original
    source record.
  </p>

  <div class="row mb-3">
    <div class="col-md-4">
      <label for="sourceTypeSelect" class="form-label">Source Type</label>
      <select id="sourceTypeSelect" class="form-select">
        <option value="hackernews" selected>Hacker News</option>
        <option value="rss">RSS</option>
        <option value="email">Email</option>
      </select>
    </div>
    <div class="col-md-4">
      <label for="originalRecordIdInput" class="form-label"
        >Original Record ID</label
      >
      <input
        type="number"
        class="form-control"
        id="originalRecordIdInput"
        placeholder="e.g., HackerNewsSource ID"
      />
    </div>
    <div class="col-md-4 align-self-end">
      <button id="diagnoseButton" class="btn btn-primary me-2">
        Diagnose Chain
      </button>
      <button id="scanIssuesButton" class="btn btn-info">
        Scan for Issues
      </button>
    </div>
  </div>

  <hr />

  <div id="scanResultsArea" class="mt-3 mb-3" style="display: none">
    <h4>Scan Results (Problematic Chains):</h4>
    <div id="scanResultsList" class="list-group">
      <!-- Scan results will be populated here -->
    </div>
    <p id="noScanIssuesFound" class="text-success" style="display: none">
      No problematic chains found in the scanned records.
    </p>
  </div>

  <h4>Diagnosis Result (for selected ID):</h4>
  <div id="diagnosisResultArea" class="mt-3">
    <p class="text-muted">
      Enter a source type and record ID, then click "Diagnose Chain", or use
      "Scan for Issues".
    </p>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const diagnoseButton = document.getElementById("diagnoseButton");
    const scanIssuesButton = document.getElementById("scanIssuesButton"); // New
    const sourceTypeSelect = document.getElementById("sourceTypeSelect");
    const originalRecordIdInput = document.getElementById(
      "originalRecordIdInput"
    );
    const diagnosisResultArea = document.getElementById("diagnosisResultArea");
    const scanResultsArea = document.getElementById("scanResultsArea"); // New
    const scanResultsList = document.getElementById("scanResultsList"); // New
    const noScanIssuesFound = document.getElementById("noScanIssuesFound"); // New

    // Add event listener for the new scan button
    scanIssuesButton.addEventListener("click", async function () {
      const sourceType = sourceTypeSelect.value;
      scanResultsArea.style.display = "block";
      scanResultsList.innerHTML =
        '<div class="spinner-border text-info" role="status"><span class="visually-hidden">Loading...</span></div> <p>Scanning for issues...</p>';
      noScanIssuesFound.style.display = "none";
      diagnosisResultArea.innerHTML =
        '<p class="text-muted">Scan results will appear above. Select an ID from scan results or enter one manually to diagnose.</p>';

      try {
        // Assuming API returns a list of ProblematicChainInfo or similar
        // Add ?limit=xxx if you want to control, e.g. /admin/api/task-chain/scan/${sourceType}?limit=50
        const response = await fetch(
          `/admin/api/task-chain/scan/${sourceType}`
        );
        const problematicChains = await response.json();

        if (!response.ok) {
          let errorMsg = `Error: ${response.status} ${response.statusText}`;
          if (problematicChains.detail) {
            errorMsg =
              typeof problematicChains.detail === "string"
                ? problematicChains.detail
                : JSON.stringify(problematicChains.detail);
          }
          throw new Error(errorMsg);
        }

        if (problematicChains.length === 0) {
          scanResultsList.innerHTML = ""; // Clear spinner
          noScanIssuesFound.style.display = "block";
        } else {
          let listHtml = "";
          problematicChains.forEach((chain) => {
            listHtml += `<a href="#" class="list-group-item list-group-item-action problematic-chain-item" data-record-id="${chain.original_record_id}">
                               <strong>ID:</strong> ${chain.original_record_id} - <strong>Status:</strong> ${chain.status_summary}<br>
                               <small>First issue: ${chain.first_issue_step} - ${chain.first_issue_description}</small>
                             </a>`;
          });
          scanResultsList.innerHTML = listHtml;
          noScanIssuesFound.style.display = "none";

          // Add click listeners to scanned items to populate diagnose input
          document
            .querySelectorAll(".problematic-chain-item")
            .forEach((item) => {
              item.addEventListener("click", function (e) {
                e.preventDefault();
                originalRecordIdInput.value = this.dataset.recordId;
                diagnoseButton.click(); // Trigger diagnosis for this ID
                window.scrollTo(0, diagnoseButton.offsetTop); // Scroll to diagnose button
              });
            });
        }
      } catch (error) {
        console.error("Scan failed:", error);
        scanResultsList.innerHTML = `<div class="alert alert-danger">Scan failed: ${error.message}</div>`;
        noScanIssuesFound.style.display = "none";
      }
    });

    diagnoseButton.addEventListener("click", async function () {
      const sourceType = sourceTypeSelect.value;
      const recordId = originalRecordIdInput.value;

      if (!sourceType || !recordId) {
        diagnosisResultArea.innerHTML =
          '<div class="alert alert-warning">Please select a source type and enter a record ID.</div>';
        return;
      }

      diagnosisResultArea.innerHTML =
        '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div> <p>Diagnosing...</p>';

      try {
        const response = await fetch(
          `/admin/api/task-chain/diagnose/${sourceType}/${recordId}`
        );
        const data = await response.json();

        if (!response.ok) {
          let errorMsg = `Error: ${response.status} ${response.statusText}`;
          if (data.detail) {
            errorMsg =
              typeof data.detail === "string"
                ? data.detail
                : JSON.stringify(data.detail);
          }
          throw new Error(errorMsg);
        }

        renderDiagnosis(data);
      } catch (error) {
        console.error("Diagnosis failed:", error);
        diagnosisResultArea.innerHTML = `<div class="alert alert-danger">Diagnosis failed: ${error.message}</div>`;
      }
    });

    function renderDiagnosis(diagnosis) {
      let html = `<h5>Diagnosis for ${diagnosis.source_type} ID: ${diagnosis.original_record_id}</h5>`;
      html += `<p><strong>Overall Status:</strong> ${diagnosis.status_summary}</p>`;

      if (diagnosis.issues && diagnosis.issues.length > 0) {
        html += "<h6>Detected Issues & Recommendations:</h6>";
        html += '<ul class="list-group">';
        diagnosis.issues.forEach((issue) => {
          html += `<li class="list-group-item">
                            <strong>Step:</strong> ${issue.step_name}<br>
                            <strong>Issue:</strong> ${issue.issue_description}<br>`;
          if (issue.recommended_retry_task_name) {
            html += `<strong>Recommended Retry:</strong> <code>${issue.recommended_retry_task_name}</code><br>`;
            if (issue.recommended_retry_task_args) {
              html += `Args: <code>${JSON.stringify(
                issue.recommended_retry_task_args
              )}</code><br>`;
            }
            if (issue.recommended_retry_task_kwargs) {
              html += `Kwargs: <code>${JSON.stringify(
                issue.recommended_retry_task_kwargs
              )}</code><br>`;
            }
            html += `<button class="btn btn-sm btn-warning mt-2 retry-button" 
                                     data-task-name="${
                                       issue.recommended_retry_task_name
                                     }"
                                     data-task-args='${JSON.stringify(
                                       issue.recommended_retry_task_args || []
                                     )}'
                                     data-task-kwargs='${JSON.stringify(
                                       issue.recommended_retry_task_kwargs || {}
                                     )}'>
                                Retry Task
                             </button>`;
          }
          html += `</li>`;
        });
        html += "</ul>";
      } else {
        html +=
          '<p class="text-success">No issues detected or chain is complete.</p>';
      }
      diagnosisResultArea.innerHTML = html;
      addRetryButtonListeners();
    }

    function addRetryButtonListeners() {
      document.querySelectorAll(".retry-button").forEach((button) => {
        button.addEventListener("click", async function () {
          const taskName = this.dataset.taskName;
          const taskArgs = JSON.parse(this.dataset.taskArgs);
          const taskKwargs = JSON.parse(this.dataset.taskKwargs);

          this.disabled = true;
          this.innerHTML =
            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Retrying...';

          try {
            const response = await fetch("/admin/api/task-chain/retry", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                // Add CSRF token if your app uses it for POST requests
              },
              body: JSON.stringify({
                task_name: taskName,
                task_args: taskArgs,
                task_kwargs: taskKwargs,
              }),
            });
            const result = await response.json();

            if (!response.ok) {
              let errorMsg = `Error: ${response.status} ${response.statusText}`;
              if (result.detail) {
                errorMsg =
                  typeof result.detail === "string"
                    ? result.detail
                    : JSON.stringify(result.detail);
              }
              throw new Error(errorMsg);
            }

            alert(
              `Success: ${result.message}. Please re-diagnose to see updated status.`
            );
            this.innerHTML = "Retried (Re-diagnose)";
            // Optionally, re-trigger diagnosis:
            // diagnoseButton.click();
          } catch (error) {
            console.error("Retry failed:", error);
            alert(`Retry failed: ${error.message}`);
            this.disabled = false;
            this.innerHTML = "Retry Task (Failed)";
          }
        });
      });
    }
  });
</script>
{% endblock %}
